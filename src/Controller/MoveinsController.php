<?php

namespace Sparefoot\MyFootService\Controller;

use Sparefoot\MyFootService\Models\ApiException;
use Sparefoot\MyFootService\Models\BidIncreaseBannerValidation;
use Sparefoot\MyFootService\Models\Features;
use Sparefoot\MyFootService\Service\User;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class MoveinsController extends AbstractRestrictedController
{
    /** @var \Genesis_Util_ActionLogger */
    private $logger;

    /**
     * Initialize controller.
     */
    protected function _init(): ?RedirectResponse
    {
        if (!\Genesis_Service_Feature::isActive(Features::CONTACTLESS_MOVEIN_BADGING, [])) {
            return $this->redirectToRoute('dashboard_index');
        }
        $this->logger = new \Genesis_Util_ActionLogger();

        $this->view->banner = [
            'showNotificationBanner' => BidIncreaseBannerValidation::isBidIncreaseBannerShown($this->getLoggedUser()->getAccount()),
        ];
        
        return null;
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/moveins
     * http://localhost:9019/moveins
     */
    #[Route('/move-ins', name: 'moveins_index')]
    public function indexAction(Request $request): Response
    {
        return $this->redirectToRoute('moveins_contactless');
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/moveins/contactless
     * http://localhost:9019/moveins/contactless
     */
    #[Route('/move-ins/contactless', name: 'contactless-move-ins')]
    public function contactlessAction(Request $request): Response
    {
        if (!\Genesis_Service_Feature::isActive(Features::CONTACTLESS_MOVEIN_BADGING, [])) {
            return $this->redirectToRoute('dashboard');
        }
        $this->view->selected = 'contactless';

        $user = $this->getLoggedUser();
        $facilities = $user->getManagableFacilities()->toArray();
        $publishedFacilities = array_values(array_filter($facilities, function (\Genesis_Entity_Facility $f) {
            return $f->getPublished();
        }));
        $contactlessFacilitiesView = array_map(function (\Genesis_Entity_Facility $f) {
            return [
                'name' => $f->getName(),
                'id' => $f->getId(),
                'contactless' => $f->getHasContactlessMoveins(),
            ];
        }, $publishedFacilities);

        // Set variables to be used in the View
        $this->view->title = 'Contactless Badge';
        $this->view->hasOnlineMoveInFmsSoftware = User::hasAccessToOmiCapableFms();
        $this->view->facilities = $contactlessFacilitiesView;
        $this->view->scripts = [
            '../dist/ember/features/assets/vendor',
            '../dist/ember/features/assets/features',
        ];

        return $this->render('moveins/contactless.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/moveins/update-contactless
     * http://localhost:9019/moveins/update-contactless
     */
    #[Route('/move-ins/update-contactless', name: 'moveins_update_contactless', methods: ['POST'])]
    public function updateContactlessAction(Request $request): JsonResponse
    {
        $user = $this->getLoggedUser();
        $jsonBody = $request->getContent();
        $decodedJson = json_decode($jsonBody);

        if (!$decodedJson) {
            throw new ApiException(ApiException::BAD_REQUEST);
        }

        $facilities = $decodedJson->facilities;
        $accessibleFacilities = $user->getManagableFacilities()->toArray();
        $accessibleFacilityIds = array_map(function ($f) { return $f->getId(); }, $accessibleFacilities);

        foreach ($facilities as $facility) {
            if (!in_array($facility->id, $accessibleFacilityIds)) {
                throw new ApiException(ApiException::FORBIDDEN, "You do not have permission to update this facility: {$facility->id}");
            }
        }

        foreach ($facilities as $facility) {
            try {
                $facilityObj = \Genesis_Service_Facility::loadById($facility->id);
                $facilityObj->setHasContactlessMoveins(boolval($facility->checked));
                $facilityObj = \Genesis_Service_Facility::save($facilityObj);

                $this->logger->logAction(
                    'toggle_contactless_facility',
                    intval($facility->originalValue),
                    intval($facility->checked),
                    $user->getId(),
                    $facility->id
                );
            } catch (\Exception $e) {
                throw new ApiException(ApiException::NOT_ACCEPTABLE, $e->getMessage());
            }
        }

        return new JsonResponse(['success' => true]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/moveins/online
     * http://localhost:9019/moveins/online
     */
    #[Route('/move-ins/online', name: 'moveins_online')]
    public function onlineAction(Request $request): Response
    {
        $this->view->selected = 'online';
        $user = $this->getLoggedUser();
        $facilities = $user->getManagableFacilities()->toArray();
        $publishedOmiCapableFacilities = array_values(array_filter($facilities, function (\Genesis_Entity_Facility $f) {
            return $f->getPublished() && $f->hasOmiCapableFms();
        }));
        $omiFacilitiesView = array_map(function (\Genesis_Entity_Facility $f) {
            return [
                'name' => $f->getName(),
                'id' => $f->getId(),
                'online' => $f->getHasOnlineMoveins(),
            ];
        }, $publishedOmiCapableFacilities);

        // Set variables to be used in the View
        $this->view->title = 'Online Move-Ins';
        $this->view->hasOnlineMoveInFmsSoftware = User::hasAccessToOmiCapableFms();
        $this->view->facilities = $omiFacilitiesView;
        $this->view->scripts = [
            '../dist/ember/features/assets/vendor',
            '../dist/ember/features/assets/features',
        ];

        return $this->render('moveins/online.html.twig', [
            'view' => $this->view,
        ]);
    }

    /**
     * Sample:
     * https://myfoot.sparefoot.com/moveins/update-online
     * http://localhost:9019/moveins/update-online
     */
    #[Route('/move-ins/update-online', name: 'moveins_update_online', methods: ['POST'])]
    public function updateOnlineAction(Request $request): JsonResponse
    {
        $user = $this->getLoggedUser();
        $jsonBody = $request->getContent();
        $decodedJson = json_decode($jsonBody);

        if (!$decodedJson) {
            throw new ApiException(ApiException::BAD_REQUEST);
        }

        $facilities = $decodedJson->facilities;
        $accessibleFacilities = $user->getManagableFacilities()->toArray();
        $accessibleFacilityIds = array_map(function ($f) { return $f->getId(); }, $accessibleFacilities);

        foreach ($facilities as $facility) {
            if (!in_array($facility->id, $accessibleFacilityIds)) {
                throw new ApiException(ApiException::FORBIDDEN, "You do not have permission to update this facility: {$facility->id}");
            }
        }

        foreach ($facilities as $facility) {
            try {
                $facilityObj = \Genesis_Service_Facility::loadById($facility->id);
                $facilityObj->setHasOnlineMoveins(boolval($facility->checked));
                $facilityObj = \Genesis_Service_Facility::save($facilityObj);

                $this->logger->logAction(
                    'toggle_facility_online_moveins',
                    intval($facility->originalValue),
                    intval($facility->checked),
                    $user->getId(),
                    $facility->id
                );
            } catch (\Exception $e) {
                throw new ApiException(ApiException::NOT_ACCEPTABLE, $e->getMessage());
            }
        }

        return new JsonResponse(['success' => true]);
    }

    protected function getTab(): string
    {
        return self::TAB_MOVE_INS;
    }
}
