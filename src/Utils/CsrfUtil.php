<?php

namespace Sparefoot\MyFootService\Utils;

use Sparefoot\MyFootService\Service\User;

class CsrfUtil
{
    /**
     * @param string $key Session token name
     *
     * @return string Returns the session token to be used
     */
    public static function getToken(string $key)
    {
        if (!User::getSession()->has($key)) {
            // Increase CSRF token entropy - Use at least 16-32 bytes
            User::getSession()->set($key, base64_encode(openssl_random_pseudo_bytes(16)));
        }

        return User::getSession()->get($key);
    }

    /**
     * @param string      $key   Session token name
     * @param string|null $value Token name to validate
     *
     * @return bool
     */
    public static function validateToken(string $key, $value)
    {
        return
            null !== User::getSession()->get($key)
            && User::getSession()->get($key) === $value;
    }
}
