<div class="ui segment">
    <h3 class="ui header">
        <i class="calendar check icon"></i>
        Customer Reservations
    </h3>
    
    {% if view.reservations is defined and view.reservations|length > 0 %}
        <div class="ui table container">
            <table class="ui celled striped table">
                <thead>
                    <tr>
                        <th>Customer Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Unit Size</th>
                        <th>Reservation Date</th>
                        <th>Move-In Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for reservation in view.reservations %}
                        <tr>
                            <td>{{ reservation.customerName }}</td>
                            <td>{{ reservation.email }}</td>
                            <td>{{ reservation.phone }}</td>
                            <td>{{ reservation.unitSize }}</td>
                            <td>{{ reservation.reservationDate|date('m/d/Y') }}</td>
                            <td>{{ reservation.moveInDate|date('m/d/Y') }}</td>
                            <td>
                                <div class="ui label {{ reservation.status == constant('\\Genesis_Entity_Transaction::BOOKING_STATE_CONFIRMED') ? 'green' : 'yellow' }}">
                                    {{ reservation.status|title }}
                                </div>
                            </td>
                            <td>
                                <div class="ui small buttons">
                                    <button class="ui button">View</button>
                                    <button class="ui button">Edit</button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="ui placeholder segment">
            <div class="ui icon header">
                <i class="calendar times outline icon"></i>
                No reservations found
            </div>
            <div class="inline">
                <div class="ui primary button">
                    <i class="plus icon"></i>
                    Add New Reservation
                </div>
            </div>
        </div>
    {% endif %}
</div>
